services:
  # Override for development
  app:
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_HOST=db
      - DB_DATABASE=swinburne
      - DB_USERNAME=swinx_user
      - DB_PASSWORD=swinx_password
      - FRANKENPHP_NUM_THREADS=auto
    volumes:
      # Mount source code for development (hot reload)
      - .:/app
      - /app/node_modules
      - /app/vendor
      # FrankenPHP/Caddy volumes
      - caddy_data:/data
      - caddy_config:/config
    command: >
      sh -c "
        php artisan config:clear &&
        php artisan route:clear &&
        php artisan view:clear &&
        php artisan migrate --force &&
        php artisan cache:clear &&
        php artisan ziggy:generate &&
        /start.sh
      "

  db:
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: swinburne
      MYSQL_USER: swinx_user
      MYSQL_PASSWORD: swinx_password
