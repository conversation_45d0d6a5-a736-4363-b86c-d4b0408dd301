const Ziggy = {"url":"http:\/\/localhost:8080","port":8080,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"select-campus.index":{"uri":"select-campus","methods":["GET","HEAD"]},"select-campus.set-current":{"uri":"select-campus\/set-current","methods":["POST"]},"curriculum_version.index":{"uri":"curriculum-versions","methods":["GET","HEAD"]},"curriculum_version.create":{"uri":"curriculum-versions\/create","methods":["GET","HEAD"]},"curriculum_version.store":{"uri":"curriculum-versions","methods":["POST"]},"curriculum_version.show":{"uri":"curriculum-versions\/{curriculum_version}","methods":["GET","HEAD"],"parameters":["curriculum_version"]},"curriculum-versions.edit":{"uri":"curriculum-versions\/{curriculum_version}\/edit","methods":["GET","HEAD"],"parameters":["curriculum_version"]},"curriculum-versions.update":{"uri":"curriculum-versions\/{curriculum_version}","methods":["PUT","PATCH"],"parameters":["curriculum_version"]},"curriculum_version.destroy":{"uri":"curriculum-versions\/{curriculum_version}","methods":["DELETE"],"parameters":["curriculum_version"]},"curriculum_version.electives":{"uri":"curriculum-versions\/{curriculumVersion}\/electives","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.available-electives":{"uri":"api\/curriculum-versions\/{curriculumVersion}\/available-electives","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.elective-slots":{"uri":"api\/curriculum-versions\/{curriculumVersion}\/elective-slots","methods":["GET","HEAD"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum-units.update-elective":{"uri":"api\/curriculum-units\/{curriculumUnit}\/update-elective","methods":["PUT"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.units.details":{"uri":"api\/units\/{unit}\/details","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.curriculum-units.recommendations":{"uri":"api\/curriculum-units\/{curriculumUnit}\/recommendations","methods":["GET","HEAD"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"google.login":{"uri":"auth\/google\/redirect","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"users.import.form":{"uri":"users\/import","methods":["GET","HEAD"]},"users.import.upload":{"uri":"users\/import\/upload","methods":["POST"]},"users.import.preview":{"uri":"users\/import\/preview","methods":["POST"]},"users.import.process":{"uri":"users\/import\/process","methods":["POST"]},"users.import.history":{"uri":"users\/import\/history","methods":["GET","HEAD"]},"users.import.debug":{"uri":"users\/import\/debug","methods":["GET","HEAD"]},"users.templates.download":{"uri":"users\/templates\/{format}","methods":["GET","HEAD"],"wheres":{"format":"simple|detailed|relationship"},"parameters":["format"]},"users.export.excel":{"uri":"users\/export\/excel","methods":["GET","HEAD"]},"users.export.excel.filtered":{"uri":"users\/export\/excel\/filtered","methods":["GET","HEAD"]},"user.index":{"uri":"users","methods":["GET","HEAD"]},"user.create":{"uri":"users\/create","methods":["GET","HEAD"]},"user.store":{"uri":"users","methods":["POST"]},"user.edit":{"uri":"users\/edit\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"user.update":{"uri":"users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"user.destroy":{"uri":"users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"user.show":{"uri":"users\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"role.index":{"uri":"roles","methods":["GET","HEAD"]},"role.create":{"uri":"roles\/create","methods":["GET","HEAD"]},"role.store":{"uri":"roles","methods":["POST"]},"role.edit":{"uri":"roles\/edit\/{role}","methods":["GET","HEAD"],"parameters":["role"],"bindings":{"role":"id"}},"role.update":{"uri":"roles\/{role}","methods":["PUT"],"parameters":["role"],"bindings":{"role":"id"}},"role.destroy":{"uri":"roles\/{role}","methods":["DELETE"],"parameters":["role"]},"role.show":{"uri":"roles\/{role}","methods":["GET","HEAD"],"parameters":["role"]},"semester.index":{"uri":"semesters","methods":["GET","HEAD"]},"semester.create":{"uri":"semesters\/create","methods":["GET","HEAD"]},"semester.store":{"uri":"semesters","methods":["POST"]},"semester.edit":{"uri":"semesters\/edit\/{semester}","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"semester.update":{"uri":"semesters\/{semester}","methods":["PUT"],"parameters":["semester"],"bindings":{"semester":"id"}},"semester.destroy":{"uri":"semesters\/{semester}","methods":["DELETE"],"parameters":["semester"],"bindings":{"semester":"id"}},"semester.show":{"uri":"semesters\/{semester}","methods":["GET","HEAD"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.activate":{"uri":"semesters\/{semester}\/activate","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.deactivate":{"uri":"semesters\/{semester}\/deactivate","methods":["POST"],"parameters":["semester"],"bindings":{"semester":"id"}},"semesters.activation-statuses":{"uri":"semesters\/activation-statuses","methods":["GET","HEAD"]},"units.export.excel":{"uri":"units\/export\/excel","methods":["GET","HEAD"]},"units.export.excel.filtered":{"uri":"units\/export\/excel\/filtered","methods":["GET","HEAD"]},"units.import":{"uri":"units\/import","methods":["GET","HEAD"]},"units.import.upload":{"uri":"units\/import\/upload","methods":["POST"]},"units.import.preview":{"uri":"units\/import\/preview","methods":["POST"]},"units.import.process":{"uri":"units\/import\/process","methods":["POST"]},"units.import.template":{"uri":"units\/import\/template\/{format}","methods":["GET","HEAD"],"parameters":["format"]},"units.import.history":{"uri":"units\/import\/history","methods":["GET","HEAD"]},"unit.index":{"uri":"units","methods":["GET","HEAD"]},"unit.create":{"uri":"units\/create","methods":["GET","HEAD"]},"unit.store":{"uri":"units","methods":["POST"]},"unit.edit":{"uri":"units\/edit\/{unit}","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"unit.update":{"uri":"units\/{unit}","methods":["PUT"],"parameters":["unit"],"bindings":{"unit":"id"}},"unit.destroy":{"uri":"units\/{unit}","methods":["DELETE"],"parameters":["unit"],"bindings":{"unit":"id"}},"unit.show":{"uri":"units\/{unit}","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"api.units.search":{"uri":"api\/units\/search","methods":["GET","HEAD"]},"api.units.validate-code":{"uri":"api\/units\/validate-code","methods":["POST"]},"api.units.validate-prerequisite-expression":{"uri":"api\/units\/validate-prerequisite-expression","methods":["POST"]},"api.units.bulk-delete":{"uri":"api\/units\/bulk-delete","methods":["DELETE"]},"syllabus.index":{"uri":"units\/{unit}\/syllabus","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.create":{"uri":"units\/{unit}\/syllabus\/create","methods":["GET","HEAD"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.store":{"uri":"units\/{unit}\/syllabus","methods":["POST"],"parameters":["unit"],"bindings":{"unit":"id"}},"syllabus.show":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["GET","HEAD"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.edit":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/edit","methods":["GET","HEAD"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.update":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["PUT"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.destroy":{"uri":"units\/{unit}\/syllabus\/{syllabus}","methods":["DELETE"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.toggle-active":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/toggle-active","methods":["PATCH"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"syllabus.clone":{"uri":"units\/{unit}\/syllabus\/{syllabus}\/clone","methods":["POST"],"parameters":["unit","syllabus"],"bindings":{"unit":"id","syllabus":"id"}},"programs.index":{"uri":"programs","methods":["GET","HEAD"]},"programs.store":{"uri":"programs","methods":["POST"]},"programs.show":{"uri":"programs\/{program}","methods":["GET","HEAD"],"parameters":["program"],"bindings":{"program":"id"}},"programs.update":{"uri":"programs\/{program}","methods":["PUT"],"parameters":["program"],"bindings":{"program":"id"}},"programs.destroy":{"uri":"programs\/{program}","methods":["DELETE"],"parameters":["program"],"bindings":{"program":"id"}},"api.programs.search":{"uri":"api\/programs\/search","methods":["GET","HEAD"]},"api.programs.bulk-delete":{"uri":"api\/programs\/bulk-delete","methods":["DELETE"]},"specializations.index":{"uri":"specializations","methods":["GET","HEAD"]},"specializations.create":{"uri":"specializations\/create","methods":["GET","HEAD"]},"specializations.edit":{"uri":"specializations\/{specialization}\/edit","methods":["GET","HEAD"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.store":{"uri":"specializations","methods":["POST"]},"specializations.show":{"uri":"specializations\/{specialization}","methods":["GET","HEAD"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.update":{"uri":"specializations\/{specialization}","methods":["PUT"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"specializations.destroy":{"uri":"specializations\/{specialization}","methods":["DELETE"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"api.specializations.destroy":{"uri":"api\/specializations\/{specialization}","methods":["DELETE"],"parameters":["specialization"],"bindings":{"specialization":"id"}},"api.specializations.bulk-delete":{"uri":"api\/specializations\/bulk-delete","methods":["DELETE"]},"api.curriculum_version.destroy":{"uri":"api\/curriculum-versions\/{curriculumVersion}","methods":["DELETE"],"parameters":["curriculumVersion"],"bindings":{"curriculumVersion":"id"}},"api.curriculum_version.update":{"uri":"api\/curriculum-versions\/{curriculumVersion}","methods":["PUT"],"parameters":["curriculumVersion"]},"curriculum_version.edit":{"uri":"curriculum-versions\/edit\/{curriculum_version}","methods":["GET","HEAD"],"parameters":["curriculum_version"]},"curriculum_version.update":{"uri":"curriculum-versions\/{curriculum_version}","methods":["PUT"],"parameters":["curriculum_version"]},"curriculum_version.export.filtered":{"uri":"curriculum-versions\/export\/excel\/filtered","methods":["GET","HEAD"]},"curriculum_unit.index":{"uri":"curriculum-units","methods":["GET","HEAD"]},"curriculum_unit.create":{"uri":"curriculum-units\/create","methods":["GET","HEAD"]},"curriculum_unit.store":{"uri":"curriculum-units","methods":["POST"]},"curriculum_unit.edit":{"uri":"curriculum-units\/edit\/{curriculum_unit}","methods":["GET","HEAD"],"parameters":["curriculum_unit"]},"curriculum_unit.update":{"uri":"curriculum-units\/{curriculum_unit}","methods":["PUT"],"parameters":["curriculum_unit"]},"curriculum_unit.destroy":{"uri":"curriculum-units\/{curriculum_unit}","methods":["DELETE"],"parameters":["curriculum_unit"]},"curriculum_unit.show":{"uri":"curriculum-units\/{curriculum_unit}","methods":["GET","HEAD"],"parameters":["curriculum_unit"]},"api.curriculum_version.store":{"uri":"api\/curriculum-versions","methods":["POST"]},"api.curriculum_version.specializations-by-program":{"uri":"api\/curriculum-versions\/specializations-by-program","methods":["GET","HEAD"]},"api.curriculum_version.by-program-specialization":{"uri":"api\/curriculum-versions\/by-program-specialization","methods":["GET","HEAD"]},"api.curriculum_version.bulk-delete":{"uri":"api\/curriculum-versions\/bulk-delete","methods":["DELETE"]},"api.curriculum_version.bulk-operations":{"uri":"api\/curriculum-versions\/bulk-operations","methods":["POST"]},"api.curriculum-units.store":{"uri":"api\/curriculum-units","methods":["POST"]},"api.curriculum-units.update":{"uri":"api\/curriculum-units\/{curriculumUnit}","methods":["PUT"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.curriculum-units.destroy":{"uri":"api\/curriculum-units\/{curriculumUnit}","methods":["DELETE"],"parameters":["curriculumUnit"],"bindings":{"curriculumUnit":"id"}},"api.curriculum-units.by-curriculum-version":{"uri":"api\/curriculum-units\/by-curriculum-version","methods":["GET","HEAD"]},"api.curriculum-units.bulk-delete":{"uri":"api\/curriculum-units\/bulk-delete","methods":["DELETE"]},"students.index":{"uri":"students","methods":["GET","HEAD"]},"students.create":{"uri":"students\/create","methods":["GET","HEAD"]},"students.store":{"uri":"students","methods":["POST"]},"students.show":{"uri":"students\/{student}","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.edit":{"uri":"students\/{student}\/edit","methods":["GET","HEAD"],"parameters":["student"],"bindings":{"student":"id"}},"students.update":{"uri":"students\/{student}","methods":["PUT"],"parameters":["student"],"bindings":{"student":"id"}},"students.destroy":{"uri":"students\/{student}","methods":["DELETE"],"parameters":["student"],"bindings":{"student":"id"}},"students.assign-program":{"uri":"students\/{student}\/assign-program","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"students.update-enrollment-status":{"uri":"students\/{student}\/update-enrollment-status","methods":["POST"],"parameters":["student"],"bindings":{"student":"id"}},"api.students.specializations":{"uri":"api\/students\/specializations","methods":["GET","HEAD"]},"api.students.curriculum-versions":{"uri":"api\/students\/curriculum-versions","methods":["GET","HEAD"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
